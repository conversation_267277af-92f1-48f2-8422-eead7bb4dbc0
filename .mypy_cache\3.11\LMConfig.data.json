{".class": "MypyFile", "_fullname": "LMConfig", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "LMConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "LMConfig.LMConfig", "name": "LMConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "LMConfig.LMConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "LMConfig", "mro": ["LMConfig.LMConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "dim", "n_layers", "n_heads", "n_kv_heads", "vocab_size", "hidden_dim", "multiple_of", "norm_eps", "max_seq_len", "rope_theta", "dropout", "flash_attn", "num_experts_per_tok", "n_routed_experts", "n_shared_experts", "scoring_func", "aux_loss_alpha", "seq_aux", "norm_topk_prob", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "LMConfig.LMConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "dim", "n_layers", "n_heads", "n_kv_heads", "vocab_size", "hidden_dim", "multiple_of", "norm_eps", "max_seq_len", "rope_theta", "dropout", "flash_attn", "num_experts_per_tok", "n_routed_experts", "n_shared_experts", "scoring_func", "aux_loss_alpha", "seq_aux", "norm_topk_prob", "kwargs"], "arg_types": ["LMConfig.LMConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.float", "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.float", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LMConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aux_loss_alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.aux_loss_alpha", "name": "aux_loss_alpha", "type": "builtins.float"}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.dim", "name": "dim", "type": "builtins.int"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.dropout", "name": "dropout", "type": "builtins.float"}}, "flash_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.flash_attn", "name": "flash_attn", "type": "builtins.bool"}}, "hidden_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.hidden_dim", "name": "hidden_dim", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_seq_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.max_seq_len", "name": "max_seq_len", "type": "builtins.int"}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "LMConfig.LMConfig.model_type", "name": "model_type", "type": "builtins.str"}}, "multiple_of": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.multiple_of", "name": "multiple_of", "type": "builtins.int"}}, "n_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.n_heads", "name": "n_heads", "type": "builtins.int"}}, "n_kv_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.n_kv_heads", "name": "n_kv_heads", "type": "builtins.int"}}, "n_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.n_layers", "name": "n_layers", "type": "builtins.int"}}, "n_routed_experts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.n_routed_experts", "name": "n_routed_experts", "type": "builtins.int"}}, "n_shared_experts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.n_shared_experts", "name": "n_shared_experts", "type": "builtins.bool"}}, "norm_eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.norm_eps", "name": "norm_eps", "type": "builtins.float"}}, "norm_topk_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.norm_topk_prob", "name": "norm_topk_prob", "type": "builtins.bool"}}, "num_experts_per_tok": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.num_experts_per_tok", "name": "num_experts_per_tok", "type": "builtins.int"}}, "rope_theta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.rope_theta", "name": "rope_theta", "type": "builtins.int"}}, "scoring_func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.scoring_func", "name": "scoring_func", "type": "builtins.str"}}, "seq_aux": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.seq_aux", "name": "seq_aux", "type": "builtins.bool"}}, "vocab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "LMConfig.LMConfig.vocab_size", "name": "vocab_size", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "LMConfig.LMConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "LMConfig.LMConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "LMConfig.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "LMConfig.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "LMConfig.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "LMConfig.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "LMConfig.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "LMConfig.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\home-repos\\train-tiny-llm\\transformer\\LMConfig.py"}