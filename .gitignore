# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
PyInstaller/

# Virtual environments
.env
venv/
*.venv
env/
*.env
*.pyenv
*.pyenv-virtualenv

# Spyder project settings
.spyderproject
.spyproject.lock

# Rope project settings
.ropeproject

# Jupyter Notebook
.ipynb_checkpoints
