{"data_mtime": 1758501253, "dep_lines": [1, 2, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers", "typing", "builtins", "_frozen_importlib", "abc", "torch", "torch._C", "transformers.configuration_utils", "transformers.utils", "transformers.utils.hub"], "hash": "de715871aa8959cebdc4f313898f67e3274bff91", "id": "LMConfig", "ignore_all": false, "interface_hash": "cf858f9f7d0a7c73525380cae320ba8cdb8b5f94", "mtime": 1758501173, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\train-tiny-llm\\transformer\\LMConfig.py", "plugin_data": null, "size": 4153, "suppressed": [], "version_id": "1.15.0"}